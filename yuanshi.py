#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ASR Subtitle Generator
A desktop application to generate subtitles for video/audio files using sherpa-onnx.
Author: Your Python Engineer
Date: 2025-06-07
"""

import customtkinter as ctk
from tkinter import filedialog
import threading
import queue
import subprocess
from pathlib import Path
import sys
import time
import os
import wave
import struct

try:
    import sherpa_onnx
    print(f"sherpa_onnx version: {sherpa_onnx.__version__}")
except ImportError:
    print("错误: sherpa-onnx 库未安装。")
    print("请通过 'pip install sherpa-onnx' 进行安装。")
    sys.exit(1)

# --- 模型配置 ---
# 定义了用户可以在 GUI 中选择的模型。
# 'name': GUI 中显示的名称。
# 'repo_id': Hugging Face 仓库 ID，sherpa-onnx 会从这里自动下载模型。
# 'filename': 模型仓库中要使用的具体模型文件名。
MODELS = {
    "中文 (FireRedAsr)": {
        "name": "中文 (FireRedAsr)",
        "type": "local",
        "model_type": "fire_red_asr",
        "path": "Models/sherpa-onnx-fire-red-asr-large-zh_en",
        "encoder": "encoder.int8.onnx",
        "decoder": "decoder.int8.onnx",
        "tokens": "tokens.txt"
    },
    "English (Nemo/Parakeet-TDT)": {
        "name": "English (Nemo/Parakeet-TDT)",
        "type": "local",
        "model_type": "nemo_ctc",
        "path": "Models/nemo-parakeet-tdt-0.6b-v2",
        "model": "model.int8.onnx",  # Nemo 模型通常只有一个 model 文件
        "tokens": "tokens.txt"
    }
}

# --- 音频读取工具 ---
def read_wave(wave_filename: str):
    """
    读取 WAV 文件并返回音频数据和采样率
    Args:
        wave_filename: WAV 文件路径
    Returns:
        tuple: (samples, sample_rate)
    """
    with wave.open(wave_filename, 'rb') as wf:
        sample_rate = wf.getframerate()
        frames = wf.getnframes()
        samples = wf.readframes(frames)

        # 将字节数据转换为 16-bit 整数数组
        if wf.getsampwidth() == 2:  # 16-bit
            samples = struct.unpack(f'<{frames}h', samples)
        elif wf.getsampwidth() == 4:  # 32-bit
            samples = struct.unpack(f'<{frames}i', samples)
        else:
            raise ValueError(f"不支持的采样位深: {wf.getsampwidth() * 8} bits")

        # 转换为 float32 并归一化到 [-1, 1]
        if wf.getsampwidth() == 2:
            samples = [s / 32768.0 for s in samples]
        elif wf.getsampwidth() == 4:
            samples = [s / 2147483648.0 for s in samples]

        return samples, sample_rate

# --- SRT 格式化工具 ---
def format_time_srt(seconds: float) -> str:
    """将秒数转换为 SRT 时间码格式 (HH:MM:SS,ms)"""
    milliseconds = int((seconds - int(seconds)) * 1000)
    seconds = int(seconds)
    minutes, seconds = divmod(seconds, 60)
    hours, minutes = divmod(minutes, 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

def segments_to_srt(segments: list) -> str:
    """将 sherpa-onnx 的识别结果片段转换为 SRT 格式的字符串"""
    srt_content = ""
    for i, seg in enumerate(segments):
        start_time = format_time_srt(seg.start)
        end_time = format_time_srt(seg.end)
        text = seg.text.strip()
        srt_content += f"{i + 1}\n"
        srt_content += f"{start_time} --> {end_time}\n"
        srt_content += f"{text}\n\n"
    return srt_content

# --- 主应用类 ---
class App(ctk.CTk):
    def __init__(self):
        super().__init__()

        self.title("ASR 字幕生成器 (Powered by Sherpa-Onnx)")
        self.geometry("800x600")
        ctk.set_appearance_mode("System")
        ctk.set_default_color_theme("blue")

        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)

        self.log_queue = queue.Queue()
        self.input_file_path = ctk.StringVar()

        self.create_widgets()
        self.check_log_queue()

    def create_widgets(self):
        """创建界面上的所有组件"""
        # -- 上方配置区域 --
        config_frame = ctk.CTkFrame(self)
        config_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        config_frame.grid_columnconfigure(1, weight=1)

        # 1. 文件选择
        ctk.CTkLabel(config_frame, text="视频/音频文件:").grid(row=0, column=0, padx=10, pady=5, sticky="w")
        self.file_entry = ctk.CTkEntry(config_frame, textvariable=self.input_file_path, state="readonly")
        self.file_entry.grid(row=0, column=1, padx=10, pady=5, sticky="ew")
        self.browse_button = ctk.CTkButton(config_frame, text="浏览...", command=self.browse_file)
        self.browse_button.grid(row=0, column=2, padx=10, pady=5)

        # 2. 模型选择
        ctk.CTkLabel(config_frame, text="选择模型:").grid(row=1, column=0, padx=10, pady=5, sticky="w")
        self.model_menu = ctk.CTkOptionMenu(config_frame, values=list(MODELS.keys()))
        self.model_menu.grid(row=1, column=1, columnspan=2, padx=10, pady=5, sticky="ew")

        # 3. 参数配置
        param_frame = ctk.CTkFrame(config_frame, fg_color="transparent")
        param_frame.grid(row=2, column=0, columnspan=3, padx=5, pady=5, sticky="ew")

        ctk.CTkLabel(param_frame, text="设备:").pack(side="left", padx=(5, 5))
        self.device_menu = ctk.CTkOptionMenu(param_frame, values=["cuda", "cpu"])
        self.device_menu.pack(side="left", padx=5)

        ctk.CTkLabel(param_frame, text="解码线程数:").pack(side="left", padx=(20, 5))
        self.threads_entry = ctk.CTkEntry(param_frame, width=60)
        self.threads_entry.insert(0, "4")
        self.threads_entry.pack(side="left", padx=5)
        
        # 4. 执行按钮
        self.generate_button = ctk.CTkButton(config_frame, text="生成字幕", command=self.start_generation_thread)
        self.generate_button.grid(row=3, column=0, columnspan=3, padx=10, pady=10)

        # -- 下方日志区域 --
        self.log_textbox = ctk.CTkTextbox(self, state="disabled", font=("monospace", 12))
        self.log_textbox.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="nsew")

    def log(self, message: str):
        """向日志文本框中安全地添加一条消息"""
        self.log_textbox.configure(state="normal")
        self.log_textbox.insert("end", message + "\n")
        self.log_textbox.see("end")
        self.log_textbox.configure(state="disabled")

    def check_log_queue(self):
        """每隔100ms检查一次日志队列，并更新GUI"""
        try:
            while True:
                message = self.log_queue.get_nowait()
                if message == "__TASK_COMPLETE__":
                    self.on_task_finished(success=True)
                elif message == "__TASK_FAILED__":
                    self.on_task_finished(success=False)
                else:
                    self.log(message)
        except queue.Empty:
            pass
        self.after(100, self.check_log_queue)
        
    def browse_file(self):
        """打开文件对话框"""
        filepath = filedialog.askopenfilename(
            title="选择一个媒体文件",
            filetypes=(("所有支持的文件", "*.mp4 *.mkv *.avi *.mov *.webm *.flv *.mp3 *.flac *.wav *.m4a"),
                       ("视频文件", "*.mp4 *.mkv *.avi *.mov *.webm *.flv"),
                       ("音频文件", "*.mp3 *.flac *.wav *.m4a"))
        )
        if filepath:
            self.input_file_path.set(filepath)
            self.log(f"已选择文件: {filepath}")

    def start_generation_thread(self):
        """启动后台线程来执行字幕生成任务"""
        input_path_str = self.input_file_path.get()
        if not input_path_str:
            self.log("错误: 请先选择一个文件！")
            return

        # 禁用按钮，防止重复点击
        self.generate_button.configure(state="disabled", text="正在处理中...")
        self.log_textbox.configure(state="normal")
        self.log_textbox.delete("1.0", "end")
        self.log_textbox.configure(state="disabled")

        # 收集参数
        params = {
            "input_path": Path(input_path_str),
            "selected_model_name": self.model_menu.get(),
            "device": self.device_menu.get(),
            "num_threads": int(self.threads_entry.get() or "1")
        }

        # 创建并启动线程
        thread = threading.Thread(target=self.run_transcription_task, args=(params,))
        thread.daemon = True
        thread.start()
        
    def on_task_finished(self, success: bool):
        """任务完成或失败后的回调函数"""
        if success:
            final_text = "任务成功完成!"
        else:
            final_text = "任务失败! 请检查日志。"
        
        self.generate_button.configure(state="normal", text="生成字幕")
        self.log("="*50)
        self.log(final_text)
        self.log("="*50)

    def run_transcription_task(self, params: dict):
        """
        在后台线程中运行的核心任务。
        包括: ffmpeg音频提取 -> sherpa-onnx模型推理 -> srt文件生成
        """
        try:
            input_path: Path = params["input_path"]
            model_config = MODELS[params["selected_model_name"]]
            device = params["device"]
            num_threads = params["num_threads"]

            # --- 1. 使用 ffmpeg 提取音频 ---
            self.log_queue.put(f"[{time.strftime('%H:%M:%S')}] Step 1/3: 使用 ffmpeg 提取音频...")
            
            # 创建一个与输入文件同目录的临时wav文件
            temp_audio_path = input_path.with_name(f"temp_{input_path.stem}.wav")
            
            ffmpeg_cmd = [
                'ffmpeg', '-y', '-i', str(input_path),
                '-vn',  # 去除视频
                '-acodec', 'pcm_s16le',  # 16-bit PCM 音频
                '-ar', '16000',  # 16kHz 采样率
                '-ac', '1',  # 单声道
                str(temp_audio_path)
            ]
            self.log_queue.put(f"执行命令: {' '.join(ffmpeg_cmd)}")
            
            process = subprocess.Popen(ffmpeg_cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, encoding='utf-8')
            for line in iter(process.stdout.readline, ''):
                self.log_queue.put(f"  [ffmpeg] {line.strip()}")
            process.wait()
            
            if process.returncode != 0:
                raise RuntimeError("ffmpeg 音频提取失败。请确保 ffmpeg 已正确安装并位于系统 PATH 中。")
            
            self.log_queue.put(f"[{time.strftime('%H:%M:%S')}] 音频已成功提取到: {temp_audio_path}")

            # --- 2. 使用 sherpa-onnx 进行语音识别 ---
            self.log_queue.put(f"\n[{time.strftime('%H:%M:%S')}] Step 2/3: 初始化 Sherpa-Onnx 识别器...")
            self.log_queue.put(f"  模型: {model_config['name']}")
            self.log_queue.put(f"  设备: {device}, 线程数: {num_threads}")
            # 根据模型类型选择加载方式
            if model_config.get('type') == 'local':
                model_path = Path(model_config['path'])
                if not model_path.exists():
                    raise FileNotFoundError(f"本地模型路径不存在: {model_path}")

                tokens_path = str(model_path / model_config['tokens'])
                self.log_queue.put(f"  从本地加载模型: {model_path}")

                # 根据模型类型使用不同的加载方法
                model_type = model_config.get('model_type', 'fire_red_asr')

                if model_type == 'fire_red_asr':
                    encoder_path = str(model_path / model_config['encoder'])
                    decoder_path = str(model_path / model_config['decoder'])

                    # 检查所有模型文件是否存在
                    for p in [encoder_path, decoder_path, tokens_path]:
                        if not Path(p).exists():
                            raise FileNotFoundError(f"模型文件不存在: {p}")

                    recognizer = sherpa_onnx.OfflineRecognizer.from_fire_red_asr(
                        encoder=encoder_path,
                        decoder=decoder_path,
                        tokens=tokens_path,
                        num_threads=num_threads,
                        provider=device,
                    )

                elif model_type == 'nemo_ctc':
                    model_file_path = str(model_path / model_config['model'])

                    # 检查所有模型文件是否存在
                    for p in [model_file_path, tokens_path]:
                        if not Path(p).exists():
                            raise FileNotFoundError(f"模型文件不存在: {p}")

                    recognizer = sherpa_onnx.OfflineRecognizer.from_nemo_ctc(
                        model=model_file_path,
                        tokens=tokens_path,
                        num_threads=num_threads,
                        provider=device,
                    )

                else:
                    raise ValueError(f"不支持的本地模型类型: {model_type}")
            else:
                raise ValueError(f"不支持的模型配置类型: {model_config.get('type')}")

            self.log_queue.put(f"\n[{time.strftime('%H:%M:%S')}] 正在创建音频流并进行解码...")
            stream = recognizer.create_stream()
            stream.accept_waveform_from_files([str(temp_audio_path)])

            result = recognizer.decode_streams([stream])
            segments = result[0].segments
            
            self.log_queue.put(f"[{time.strftime('%H:%M:%S')}] 解码完成!")

            # --- 3. 生成并保存 SRT 文件 ---
            self.log_queue.put(f"\n[{time.strftime('%H:%M:%S')}] Step 3/3: 生成并保存 SRT 字幕文件...")
            srt_content = segments_to_srt(segments)
            srt_path = input_path.with_suffix(".srt")

            with open(srt_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            
            self.log_queue.put(f"字幕文件已成功保存到: {srt_path}")
            
            # --- 4. 清理 ---
            try:
                os.remove(temp_audio_path)
                self.log_queue.put(f"已删除临时音频文件: {temp_audio_path}")
            except OSError as e:
                self.log_queue.put(f"警告: 删除临时文件失败 - {e}")

            self.log_queue.put("__TASK_COMPLETE__")

        except Exception as e:
            self.log_queue.put("\n" + "="*20 + " 发生错误 " + "="*20)
            import traceback
            self.log_queue.put(traceback.format_exc())
            self.log_queue.put("__TASK_FAILED__")


if __name__ == "__main__":
    # 检查 ffmpeg 是否存在
    try:
        subprocess.run(["ffmpeg", "-version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: ffmpeg 未找到。")
        print("请确保 ffmpeg 已经安装，并且其路径已经添加到了系统的 PATH 环境变量中。")
        # 在GUI中显示错误信息会更好，但为简单起见，这里直接退出
        # 在实际应用中，可以在GUI启动时进行此检查并弹窗提示
        sys.exit(1)

    app = App()
    app.mainloop()